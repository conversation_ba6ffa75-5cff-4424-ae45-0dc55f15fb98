#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import tkinter as tk
from tkinter import messagebox, ttk
import threading
import asyncio
import os
from datetime import datetime
import json
from .audio_controller import AudioController

class IndexPage:

    def __init__(self, main_controller):
        """初始化首页"""
        self.main_controller = main_controller
        self.root = main_controller.root
        
        # WbServer 相关属性
        self.wb_server = None
        self.wb_server_thread = None
        self.message_update_job = None  # 消息历史更新任务
        
        # WebSocket控制界面组件
        self.wb_server_frame = None
        self.server_status_label = None
        self.start_server_button = None
        self.stop_server_button = None
        self.client_count_label = None
        self.message_listbox = None
        self.send_command_frame = None
        self.command_var = None
        self.send_button = None
        
        # 初始化WbServer
        self._init_wb_server()
        
        # 初始化音频控制器
        self.audio_controller = AudioController()
        
        # TTS即输即播相关状态
        self.tts_input_var = None
        self.tts_convert_button = None
        self.tts_stop_button = None
        self.tts_status_label = None
        self.is_tts_converting = False
        
    def _init_wb_server(self):
        """初始化WbServer实例"""
        try:
            from src.plugin.wb_server_plugin import WbServer
            self.wb_server = WbServer()
            print("✅ WbServer 初始化成功")
        except Exception as e:
            print(f"❌ WbServer 初始化失败: {e}")
            self.wb_server = None
        
    def setup_home_tab(self):
        """设置首页标签"""
        home_frame = ttk.Frame(self.main_controller.notebook)
        self.main_controller.notebook.add(home_frame, text="      首页      ")
        
        # 创建主容器
        main_container = ttk.Frame(home_frame)
        main_container.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 控制按钮区
        button_frame = ttk.LabelFrame(main_container, text="直播控制", padding="15")
        button_frame.pack(fill="x", pady=(0, 15))
        
        # 创建按钮布局
        buttons_container = ttk.Frame(button_frame)
        buttons_container.pack(fill="x")
        
        # URL输入
        url_frame = ttk.Frame(buttons_container)
        url_frame.pack(fill="x", pady=(0, 10))
        
        ttk.Label(url_frame, text="直播地址:").pack(side="left", padx=(0, 10))
        self.main_controller.url_var = tk.StringVar(value="http://k2m.1jifu.cn/fbt?index=0&lesson_id=2155&uid=86")
        url_entry = ttk.Entry(url_frame, textvariable=self.main_controller.url_var, width=50)
        url_entry.pack(side="left", fill="x", expand=True)
        
        # 按钮行
        button_row = ttk.Frame(buttons_container)
        button_row.pack(fill="x", pady=(10, 0))
        
        # 初始化浏览器按钮
        self.main_controller.init_button = ttk.Button(button_row, text="初始化浏览器", 
                                                     command=self.initialize_browser, width=15)
        self.main_controller.init_button.pack(side="left", padx=(0, 10))
        
        # 主播稿按钮
        self.main_controller.voice_button = ttk.Button(button_row, text="主播稿", 
                                                      command=self.generate_voice_files, width=15)
        self.main_controller.voice_button.pack(side="left", padx=(0, 10))
        
        # 开始直播按钮
        self.main_controller.start_button = ttk.Button(button_row, text="主播上线", 
                                                      command=self.toggle_stream_status, 
                                                      state="disabled", width=15)
        self.main_controller.start_button.pack(side="left")
        
        # TTS即输即播区域
        self._setup_instant_tts_controls(main_container)
        
        # WbServer 控制区域
        self._setup_wb_server_controls(main_container)
        
        return home_frame
    
    def initialize_browser(self):
        if not self.main_controller.is_initialized:
            # 启动浏览器
            url = self.main_controller.url_var.get().strip()
            if not url:
                messagebox.showerror("错误", "请输入直播地址")
                return
            self.main_controller.init_button.configure(state="disabled")
            
            # 启动 WbServer
            self.wb_server_thread = threading.Thread(target=self._start_wb_server_wrapper, daemon=True)
            self.wb_server_thread.start() 
            
            # 启动状态更新
            self._start_status_update()
            
            # 启动浏览器
            threading.Thread(target=self._initialize_browser_wrapper, 
                            args=(url,), daemon=True).start()
        else:
            self.main_controller.init_button.configure(state="disabled")
            threading.Thread(target=self._close_browser_wrapper, daemon=True).start()
    
    def generate_voice_files(self):
        """生成主播稿语音文件"""
        try:
            # 检查直播地址是否已设置
            if not hasattr(self.main_controller, 'url_var') or not self.main_controller.url_var.get().strip():
                messagebox.showerror("错误", "请先设置直播地址！")
                return
            
            # 检查语音处理器是否已初始化
            if not hasattr(self.main_controller, 'voice_processor') or not self.main_controller.voice_processor:
                messagebox.showerror("错误", "语音处理器未初始化，请检查插件安装或重启程序")
                return
            
            # 禁用按钮并显示进度
            self.main_controller.voice_button.configure(text="生成中...", state="disabled")
            
            # 在新线程中执行语音文件生成
            thread = threading.Thread(target=self._generate_voice_files_async, daemon=True)
            thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动语音文件生成失败:\n{str(e)}")
            print(f"❌ 启动语音文件生成失败: {e}")
    
    def toggle_stream_status(self):
        try:
            if not self.wb_server or not self.wb_server.is_running:
                messagebox.showwarning("警告", "请先初始化浏览器和WbServer")
                return
            
            # 获取当前监听状态 
            listener_status = self.wb_server.get_listener_status()
            is_streaming = listener_status.get("is_listening", False)
            
            if not is_streaming:
                # 主播上线
                self.main_controller.start_button.configure(text="启动中...", state="disabled")
                threading.Thread(target=self._start_live_streaming_async, daemon=True).start()
            else:
                # 停止直播
                self.main_controller.start_button.configure(text="停止中...", state="disabled")
                threading.Thread(target=self._stop_live_streaming_async, daemon=True).start()
                
        except Exception as e:
            error_msg = f"切换直播状态失败: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
    
    def _setup_wb_server_controls(self, parent):
        """设置 WbServer 控制界面"""
        if not self.wb_server:
            # 如果WbServer未初始化，显示错误信息
            error_frame = ttk.LabelFrame(parent, text="WebSocket服务器 (未可用)", padding="10")
            error_frame.pack(fill="x", pady=(0, 15))
            error_label = ttk.Label(error_frame, text="❌ WbServer插件初始化失败", foreground="red")
            error_label.pack()
            return
            
        # WbServer 控制区域
        self.wb_server_frame = ttk.LabelFrame(parent, text="WebSocket服务器控制", padding="15")
        self.wb_server_frame.pack(fill="x", pady=(0, 15))
        
        # 服务器控制行
        control_frame = ttk.Frame(self.wb_server_frame)
        control_frame.pack(fill="x", pady=(0, 10))
        
        # 服务器状态
        self.server_status_label = ttk.Label(control_frame, text="状态: 未启动", foreground="red")
        self.server_status_label.pack(side="left", padx=(0, 15))
        
        # 客户端连接数
        self.client_count_label = ttk.Label(control_frame, text="连接数: 0")
        self.client_count_label.pack(side="left", padx=(0, 15))
        
        # 启动服务器按钮
        self.start_server_button = ttk.Button(control_frame, text="启动服务器", 
                                            command=self._start_wb_server_wrapper, width=12)
        self.start_server_button.pack(side="left", padx=(0, 5))
        
        # 停止服务器按钮
        self.stop_server_button = ttk.Button(control_frame, text="停止服务器", 
                                           command=self._stop_wb_server_wrapper, 
                                           state="disabled", width=12)
        self.stop_server_button.pack(side="left", padx=(0, 10))
        
        # 命令发送区域
        self.send_command_frame = ttk.Frame(self.wb_server_frame)
        self.send_command_frame.pack(fill="x", pady=(10, 0))
        
        ttk.Label(self.send_command_frame, text="发送命令:").pack(side="left", padx=(0, 5))
        
        # 命令选择下拉框
        self.command_var = tk.StringVar(value="start")
        command_combo = ttk.Combobox(self.send_command_frame, textvariable=self.command_var,
                                   values=["start", "pause", "goon", "xm", "help", "switch"],
                                   state="readonly", width=10)
        command_combo.pack(side="left", padx=(0, 5))
        
        # 发送按钮
        self.send_button = ttk.Button(self.send_command_frame, text="发送", 
                                    command=self._send_command_wrapper, 
                                    state="disabled", width=8)
        self.send_button.pack(side="left", padx=(0, 10))
        
        # 消息历史显示区域
        history_frame = ttk.LabelFrame(self.wb_server_frame, text="消息历史", padding="10")
        history_frame.pack(fill="both", expand=True, pady=(10, 0))
        
        # 创建滚动文本框
        text_frame = ttk.Frame(history_frame)
        text_frame.pack(fill="both", expand=True)
        
        self.message_listbox = tk.Text(text_frame, height=8, wrap=tk.WORD, state="disabled")
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.message_listbox.yview)
        self.message_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.message_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 清空历史按钮
        clear_button = ttk.Button(history_frame, text="清空历史", 
                                command=self._clear_message_history, width=10)
        clear_button.pack(pady=(5, 0))
    
    def _setup_instant_tts_controls(self, parent):
        """设置TTS即输即播控制界面"""
        # TTS即输即播区域
        tts_frame = ttk.LabelFrame(parent, text="TTS即输即播", padding="15")
        tts_frame.pack(fill="x", pady=(0, 15))
        
        # 输入行
        input_frame = ttk.Frame(tts_frame)
        input_frame.pack(fill="x", pady=(0, 10))
        
        ttk.Label(input_frame, text="输入文本:").pack(side="left", padx=(0, 10))
        
        # 文本输入框
        self.tts_input_var = tk.StringVar()
        tts_entry = ttk.Entry(input_frame, textvariable=self.tts_input_var, width=50)
        tts_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        # 绑定回车键事件
        tts_entry.bind('<Return>', self._on_tts_enter_pressed)
        
        # 控制按钮行
        control_frame = ttk.Frame(tts_frame)
        control_frame.pack(fill="x", pady=(0, 5))
        
        # 转换/播放按钮
        self.tts_convert_button = ttk.Button(control_frame, text="转换并播放", 
                                           command=self._start_tts_conversion, width=12)
        self.tts_convert_button.pack(side="left", padx=(0, 10))
        
        # 停止播放按钮
        self.tts_stop_button = ttk.Button(control_frame, text="停止播放", 
                                        command=self._stop_tts_playback, 
                                        state="disabled", width=12)
        self.tts_stop_button.pack(side="left")
        
        # 状态显示
        self.tts_status_label = ttk.Label(tts_frame, text="准备就绪", 
                                        font=("Arial", 9), foreground="gray")
        self.tts_status_label.pack(anchor="w")
    
    def _on_tts_enter_pressed(self, event):
        """处理文本框回车键事件"""
        self._start_tts_conversion()
    
    def _start_tts_conversion(self):
        """开始TTS转换和播放"""
        if self.is_tts_converting:
            return
        
        text = self.tts_input_var.get().strip()
        if not text:
            messagebox.showwarning("输入错误", "请输入要转换的文本")
            return
        
        # 检查文本长度
        if len(text) > 500:
            messagebox.showwarning("文本过长", "输入文本长度不能超过500字符")
            return
        
        # 更新UI状态
        self.is_tts_converting = True
        self.tts_convert_button.configure(state="disabled")
        self.tts_status_label.configure(text="正在转换语音...", foreground="orange")
        
        # 在后台线程中执行TTS转换
        threading.Thread(target=self._perform_tts_conversion, 
                        args=(text,), daemon=True).start()
    
    def _perform_tts_conversion(self, text: str):
        """在后台线程中执行TTS转换"""
        try:
            # 使用异步方式调用TTS插件
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                self.main_controller.tts_plugin.synthesize_async(text)
            )
            
            loop.close()
            
            # 在主线程中处理结果
            self.root.after(0, self._handle_tts_result, result)
            
        except Exception as e:
            error_msg = f"TTS转换失败: {str(e)}"
            self.root.after(0, self._handle_tts_error, error_msg)
    
    def _handle_tts_result(self, result: dict):
        """处理TTS转换结果（在主线程中执行）"""
        try:
            if result.get('success', False):
                audio_file = result.get('output_file')
                from_cache = result.get('from_cache', False)
                
                if audio_file:
                    cache_info = "（来自缓存）" if from_cache else "（新生成）"
                    self.tts_status_label.configure(
                        text=f"转换成功{cache_info}，开始播放...", 
                        foreground="green"
                    )
                    
                    # 自动播放音频
                    success = self.audio_controller.play(
                        audio_file, 
                        completion_callback=self._on_playback_complete
                    )
                    
                    if success:
                        # 更新按钮状态
                        self.tts_stop_button.configure(state="normal")
                        self.tts_status_label.configure(
                            text=f"正在播放: {result.get('text', '')[:30]}...",
                            foreground="blue"
                        )
                    else:
                        self._reset_tts_ui_state()
                        self.tts_status_label.configure(
                            text="播放失败", foreground="red"
                        )
                else:
                    self._reset_tts_ui_state()
                    self.tts_status_label.configure(
                        text="转换结果无效", foreground="red"
                    )
            else:
                error_msg = result.get('error', '未知错误')
                self._handle_tts_error(error_msg)
                
        except Exception as e:
            self._handle_tts_error(f"处理结果失败: {str(e)}")
    
    def _handle_tts_error(self, error_msg: str):
        """处理TTS错误（在主线程中执行）"""
        self._reset_tts_ui_state()
        self.tts_status_label.configure(text=f"错误: {error_msg}", foreground="red")
        print(f"❌ TTS即输即播错误: {error_msg}")
    
    def _stop_tts_playback(self):
        """停止TTS播放"""
        try:
            self.audio_controller.stop()
            self._reset_tts_ui_state()
            self.tts_status_label.configure(text="播放已停止", foreground="gray")
        except Exception as e:
            print(f"❌ 停止播放失败: {e}")
    
    def _on_playback_complete(self):
        """播放完成回调（在后台线程中执行）"""
        # 在主线程中更新UI
        self.root.after(0, self._on_playback_complete_ui_update)
    
    def _on_playback_complete_ui_update(self):
        """播放完成后的UI更新（在主线程中执行）"""
        self._reset_tts_ui_state()
        self.tts_status_label.configure(text="播放完成", foreground="green")
    
    def _reset_tts_ui_state(self):
        """重置TTS UI状态"""
        self.is_tts_converting = False
        self.tts_convert_button.configure(state="normal")
        self.tts_stop_button.configure(state="disabled")
    
    def _start_wb_server_wrapper(self):
        """WbServer 启动包装器"""
        if not self.wb_server:
            messagebox.showerror("错误", "WbServer 未初始化")
            return
            
        # 在后台线程中启动WebSocket服务器
        def start_server_task():
            try:
                # 创建新的事件循环用于WebSocket服务器
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # 保存事件循环引用到WbServer实例
                self.wb_server._loop = loop
                
                # 设置当前URL
                url = self.main_controller.url_var.get().strip()
                if url:
                    self.wb_server.set_current_url(url)
                
                # 启动服务器并保持运行
                loop.run_until_complete(self.wb_server.start_server())
                
                # 保持事件循环运行，直到服务器停止
                try:
                    loop.run_forever()
                except Exception as e:
                    print(f"⚠️ 事件循环异常退出: {e}")
                finally:
                    # 清理事件循环
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()
                    if pending:
                        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                    loop.close()
                    
            except Exception as e:
                error_msg = f"WebSocket服务器启动失败: {str(e)}"
                print(f"❌ {error_msg}")
                self.root.after(0, lambda: self._on_server_start_error(error_msg))
        
        # 启动后台线程
        self.wb_server_thread = threading.Thread(target=start_server_task, daemon=True)
        self.wb_server_thread.start()
        
        # 更新UI状态
        self.start_server_button.configure(state="disabled")
        self.server_status_label.configure(text="状态: 启动中...", foreground="orange")
        
        # 开始状态更新
        self._start_status_update()
    
    def _stop_wb_server_wrapper(self):
        """WbServer 停止包装器"""
        if not self.wb_server:
            return
            
        # 在后台线程中停止WebSocket服务器
        def stop_server_task():
            try:
                # 如果服务器在独立事件循环中运行，需要在该循环中停止
                if hasattr(self.wb_server, '_loop') and self.wb_server._loop:
                    # 在服务器的事件循环中停止
                    future = asyncio.run_coroutine_threadsafe(
                        self.wb_server.stop_server(), 
                        self.wb_server._loop
                    )
                    future.result(timeout=10)  # 等待最多10秒
                    # 停止事件循环
                    self.wb_server._loop.call_soon_threadsafe(self.wb_server._loop.stop)
                else:
                    # 创建临时事件循环来停止服务器
                    asyncio.run(self.wb_server.stop_server())
                
                print("✅ WebSocket服务器已停止")
                self.root.after(0, self._on_server_stopped)
            except Exception as e:
                error_msg = f"WebSocket服务器停止失败: {str(e)}"
                print(f"❌ {error_msg}")
                self.root.after(0, lambda: self._on_server_stop_error(error_msg))
        
        # 启动后台线程
        thread = threading.Thread(target=stop_server_task, daemon=True)
        thread.start()
        
        # 更新UI状态
        self.stop_server_button.configure(state="disabled")
        self.server_status_label.configure(text="状态: 停止中...", foreground="orange")
    
    def _send_command_wrapper(self):
        """发送命令包装器"""
        if not self.wb_server or not self.wb_server.is_running:
            messagebox.showwarning("警告", "WebSocket服务器未运行")
            return
            
        command = self.command_var.get()
        
        def send_command_task():
            try:
                if command == "start":
                    asyncio.run(self.wb_server.send_start_command())
                elif command == "pause":
                    asyncio.run(self.wb_server.send_pause_command())
                elif command == "goon":
                    asyncio.run(self.wb_server.send_goon_command())
                elif command == "xm":
                    asyncio.run(self.wb_server.send_xm_command())
                elif command == "help":
                    asyncio.run(self.wb_server.send_help_command())
                elif command == "switch":
                    asyncio.run(self.wb_server.send_switch_command())
                    
                print(f"✅ 命令 '{command}' 发送成功")
            except Exception as e:
                error_msg = f"命令发送失败: {str(e)}"
                print(f"❌ {error_msg}")
                self.root.after(0, lambda: messagebox.showerror("命令发送失败", error_msg))
        
        # 在后台线程中发送命令
        thread = threading.Thread(target=send_command_task, daemon=True)
        thread.start()
    
    def _clear_message_history(self):
        """清空消息历史"""
        if self.wb_server:
            self.wb_server.clear_message_history()
        
        if self.message_listbox:
            self.message_listbox.configure(state="normal")
            self.message_listbox.delete(1.0, tk.END)
            self.message_listbox.configure(state="disabled")
    
    def _start_status_update(self):
        """开始定期更新状态"""
        self._update_server_status()
        
        # 每1秒更新一次状态
        self.message_update_job = self.root.after(1000, self._start_status_update)
    
    def _update_server_status(self):
        """更新服务器状态显示"""
        if not self.wb_server:
            return
            
        # 获取服务器状态
        status = self.wb_server.get_server_status()
        
        # 更新状态标签
        if status["is_running"]:
            status_text = f"状态: 运行中 (端口:{status['port']})"
            self.server_status_label.configure(text=status_text, foreground="green")
            self.start_server_button.configure(state="disabled")
            self.stop_server_button.configure(state="normal")
            self.send_button.configure(state="normal")
        else:
            self.server_status_label.configure(text="状态: 未启动", foreground="red")
            self.start_server_button.configure(state="normal")
            self.stop_server_button.configure(state="disabled")
            self.send_button.configure(state="disabled")
        
        # 更新客户端连接数
        client_count = status["client_count"]
        self.client_count_label.configure(text=f"连接数: {client_count}")
        
        # 更新消息历史
        self._update_message_history()
    
    def _update_message_history(self):
        """更新消息历史显示"""
        if not self.wb_server or not self.message_listbox:
            return
            
        # 获取消息历史
        history = self.wb_server.get_message_history()
        
        # 清空现有内容
        self.message_listbox.configure(state="normal")
        self.message_listbox.delete(1.0, tk.END)
        
        # 显示最新的消息
        for msg in history[-20:]:  # 只显示最新20条消息
            timestamp = msg["timestamp"][:19]  # 只显示到秒
            direction = "⬅️" if msg["direction"] == "received" else "➡️"
            msg_type = msg["type"]
            client_info = msg.get("client_info", "")
            
            # 格式化消息内容
            content_str = json.dumps(msg["content"], ensure_ascii=False, separators=(',', ':'))
            if len(content_str) > 100:
                content_str = content_str[:100] + "..."
                
            line = f"[{timestamp}] {direction} {msg_type} {client_info}\n{content_str}\n\n"
            self.message_listbox.insert(tk.END, line)
        
        self.message_listbox.configure(state="disabled")
        self.message_listbox.see(tk.END)  # 滚动到底部
    
    def _initialize_browser_wrapper(self, url):
        """浏览器初始化异步包装器"""
        try:
            asyncio.run(self._initialize_browser_async(url))
        except Exception as e:
            error_msg = f"浏览器初始化包装器异常: {str(e)}"
            print(f"❌ {error_msg}")
            self.root.after(0, lambda: self._on_init_error(error_msg))
    
    async def _initialize_browser_async(self, url):
        """异步初始化浏览器"""
        browser_success = False
        try:
            self.main_controller.browser = self.main_controller.iPhone14LandscapeBrowserPlugin()
            browser_success = await self.main_controller.browser.start_browser(headless=False)
            
            await self.main_controller.browser.start_live_stream(url)
            self.main_controller.is_initialized = True
        except Exception as e:
            error_msg = f"浏览器初始化异步操作异常: {type(e).__name__}: {str(e)}"
            print(f"❌ {error_msg}")
        
        # 无论浏览器是否成功，都尝试生成zhibo语音文件
        if self.main_controller.voice_processor:
            self.main_controller.voice_processor.generate_zhibo_voice_data(url)
        else:
            print("❌ 语音处理器未初始化")
        self.root.after(0, self._on_browser_initialized_and_started)
    
    def _close_browser_wrapper(self):
        """关闭浏览器包装器"""
        try:
            asyncio.run(self._close_browser_async())
        except Exception as e:
            error_msg = f"关闭异常: {str(e)}"
            print(f"❌ 浏览器关闭包装器异常: {error_msg}")
            self.root.after(0, lambda: self._on_close_error(error_msg))
    
    async def _close_browser_async(self):
        """异步关闭浏览器"""
        try:
            print("🔄 开始异步关闭浏览器...")
            self.main_controller.is_initialized = False
            
            # 关闭浏览器
            if hasattr(self.main_controller, 'browser') and self.main_controller.browser:
                print("🔄 调用浏览器插件的关闭方法...")
                success = await self.main_controller.browser.close()
                
                if success:
                    print("✅ 浏览器插件关闭成功")
                else:
                    print("⚠️ 浏览器插件关闭返回失败，但继续处理")
                    
                # 清空浏览器引用
                self.main_controller.browser = None
            else:
                print("⚠️ 浏览器对象不存在或已清空")
            
            print("✅ 浏览器异步关闭流程完成")
            self.root.after(0, self._on_browser_closed)
            
        except Exception as e:
            error_msg = f"关闭异步操作异常: {type(e).__name__}: {str(e)}"
            print(f"❌ {error_msg}")
            # 即使出错也要清空浏览器引用
            if hasattr(self.main_controller, 'browser'):
                self.main_controller.browser = None
            self.root.after(0, lambda: self._on_close_error(error_msg))
    
    def _on_browser_closed(self):
        """浏览器关闭成功处理"""
        self.main_controller.init_button.configure(text="初始化浏览器", state="normal")
        self.main_controller.start_button.configure(text="主播上线", state="disabled")
    
    def _on_close_error(self, error_msg):
        """关闭失败时的错误处理"""
        messagebox.showerror("关闭错误", error_msg)
        self.main_controller.init_button.configure(state="normal")
    
    def _on_init_error(self, error_msg):
        """初始化失败时的错误处理"""
        messagebox.showerror("初始化错误", error_msg)  
        # 重置按钮状态
        self.main_controller.init_button.configure(text="初始化浏览器", state="normal")
    
    def _on_browser_initialized_and_started(self):
        """浏览器初始化成功处理"""
        self.main_controller.init_button.configure(text="关闭浏览器", state="normal")
        self.main_controller.start_button.configure(text="主播上线", state="normal")
    
    def _start_live_streaming_async(self):
        """异步启动直播"""
        try:
            print("🎵 启动直播功能...")
            
            # 获取缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            print(f"🔑 使用缓存键: {cache_key}")
            
            # 启动监听服务
            success = self.wb_server.start_listener(cache_key)
            
            if success:
                print("✅ 监听服务启动成功")
                # 在主线程中更新UI
                self.root.after(0, self._on_streaming_started)
            else:
                print("❌ 监听服务启动失败")
                # 在主线程中更新UI
                self.root.after(0, lambda: self._on_streaming_error("监听服务启动失败"))
                
        except Exception as e:
            error_msg = f"启动直播异常: {str(e)}"
            print(f"❌ {error_msg}")
            # 在主线程中更新UI
            self.root.after(0, lambda: self._on_streaming_error(error_msg))
    
    def _stop_live_streaming_async(self):
        """异步停止直播"""
        try:
            print("🛑 停止直播功能...")
            
            # 停止监听服务
            success = self.wb_server.stop_listener()
            
            if success:
                print("✅ 监听服务停止成功")
                # 在主线程中更新UI
                self.root.after(0, self._on_streaming_stopped)
            else:
                print("⚠️ 监听服务停止返回False，但继续处理")
                # 即使返回False也更新UI
                self.root.after(0, self._on_streaming_stopped)
                
        except Exception as e:
            error_msg = f"停止直播异常: {str(e)}"
            print(f"❌ {error_msg}")
            # 在主线程中更新UI
            self.root.after(0, lambda: self._on_streaming_error(error_msg))
    
    def _on_streaming_started(self):
        """直播启动成功处理"""
        self.main_controller.start_button.configure(text="主播下线", state="normal")
        print("✅ 主播上线成功，界面已更新")
    
    def _on_streaming_stopped(self):
        """直播停止成功处理"""
        self.main_controller.start_button.configure(text="主播上线", state="normal")
        print("✅ 主播下线成功，界面已更新")
    
    def _on_streaming_error(self, error_msg):
        """直播操作失败处理"""
        self.main_controller.start_button.configure(text="主播上线", state="normal")
        messagebox.showerror("直播控制错误", error_msg)
        print(f"❌ 直播控制错误: {error_msg}")
    
    def _generate_voice_files_async(self):
        """异步生成语音文件"""
        try:
            # 获取当前URL
            url = self.main_controller.url_var.get().strip()
            
            # 更新进度状态
            self.root.after(0, lambda: self.main_controller.voice_button.configure(text="分析中..."))
            
            # 调用语音处理器生成语音数据
            self.main_controller.voice_processor.generate_zhibo_voice_data(url)
            
            # 获取生成的语音缓存信息
            cache_key = self.main_controller._get_ai_cache_key()
            voice_cache_info = self.main_controller.voice_processor.get_voice_cache_info(cache_key)
            
            if not voice_cache_info:
                # 没有语音缓存信息
                self.root.after(0, lambda: self._on_voice_generation_error("未找到语音缓存信息"))
                return

            # 检查是否是新的优化版缓存
            if voice_cache_info.get("md5_encoding", False):
                # 新版：从主播稿文件中获取文本数据
                print("🔧 检测到优化版缓存，使用MD5即时编码模式")

                # 从主播稿文件中提取文本
                zhubo_file_path = self.main_controller.voice_processor._get_zhubo_file_path(cache_key)
                if not os.path.exists(zhubo_file_path):
                    print("❌ 未找到主播稿文件")
                    self.root.after(0, lambda: self.main_controller.voice_button.configure(text="❌ 未找到主播稿"))
                    return

                zhubo_data = self.main_controller.voice_processor._load_zhubo_data(zhubo_file_path)
                all_texts = self.main_controller.voice_processor._extract_texts_from_zhubo_data(zhubo_data)

                # 检查缺失的语音文件
                missing_texts = self.main_controller.voice_processor._check_missing_voice_files(all_texts)
                total_count = len(all_texts)
                missing_count = len(missing_texts)
                existing_count = total_count - missing_count

                print(f"📊 语音文件统计：总计 {total_count} 条，已存在 {existing_count} 条，缺失 {missing_count} 条")

                if missing_count > 0:
                    # 发现缺失文件，自动调用TTS生成
                    print(f"🔧 检测到 {missing_count} 个缺失语音文件，开始自动生成...")
                    self.root.after(0, lambda: self.main_controller.voice_button.configure(text="TTS生成中..."))

                    # 调用语音处理器的TTS生成功能
                    def progress_callback(message):
                        # 更新按钮文本显示进度
                        display_text = f"TTS: {message[:15]}..." if len(message) > 15 else f"TTS: {message}"
                        self.root.after(0, lambda: self.main_controller.voice_button.configure(text=display_text))
                        print(f"🎵 TTS进度: {message}")

                    def completion_callback(result):
                        # TTS生成完成后的处理
                        self.root.after(0, lambda: self._on_tts_generation_complete(result, total_count, existing_count))

                    # 启动TTS生成（传入文本数据）
                    asyncio.create_task(
                        self.main_controller.voice_processor.generate_missing_voice_files_async(
                            all_texts, progress_callback, completion_callback
                        )
                    )
                else:
                    # 所有文件都存在
                    print("✅ 所有语音文件都已存在，无需生成")
                    self.root.after(0, lambda: self.main_controller.voice_button.configure(text="✅ 语音文件完整"))
            else:
                # 旧版：使用voice_data映射
                print("📋 使用传统voice_data映射模式")
                voice_data = voice_cache_info.get("voice_data", {})

                # 检查语音文件完整性
                existence_check = self.main_controller.voice_processor.check_voice_files_exist(voice_data)
                total_count = len(voice_data)
                existing_count = sum(1 for exists in existence_check.values() if exists)
                missing_count = total_count - existing_count

                print(f"📊 语音文件统计：总计 {total_count} 条，已存在 {existing_count} 条，缺失 {missing_count} 条")

                if missing_count > 0:
                    # 发现缺失文件，自动调用TTS生成
                    print(f"🔧 检测到 {missing_count} 个缺失语音文件，开始自动生成...")
                    self.root.after(0, lambda: self.main_controller.voice_button.configure(text="TTS生成中..."))

                    # 调用语音处理器的TTS生成功能
                    def progress_callback(message):
                        # 更新按钮文本显示进度
                        display_text = f"TTS: {message[:15]}..." if len(message) > 15 else f"TTS: {message}"
                        self.root.after(0, lambda: self.main_controller.voice_button.configure(text=display_text))
                        print(f"🎵 TTS进度: {message}")

                    def completion_callback(result):
                        # TTS生成完成后的处理
                        self.root.after(0, lambda: self._on_tts_generation_complete(result, total_count, existing_count))

                    # 启动TTS生成
                    asyncio.create_task(
                        self.main_controller.voice_processor.generate_missing_voice_files_async(
                            None, progress_callback, completion_callback
                        )
                    )
                else:
                    # 所有文件都存在
                    print("✅ 所有语音文件都已存在，无需生成")
                    self.root.after(0, lambda: self.main_controller.voice_button.configure(text="✅ 语音文件完整"))

        except Exception as e:
            error_msg = f"生成语音文件失败:\n{str(e)}"
            print(f"❌ 异步生成语音文件失败: {e}")
            # 在主线程中显示错误
            self.root.after(0, lambda: self._on_voice_generation_error(error_msg))
    
    def _on_voice_generation_complete(self, message, all_files_exist):
        """语音文件生成完成处理"""
        # 恢复按钮状态
        self.main_controller.voice_button.configure(text="主播稿", state="normal")
        
        # 显示结果消息
        if all_files_exist:
            messagebox.showinfo("主播稿语音文件", message)
        else:
            messagebox.showwarning("主播稿语音文件", message)
        
        print("✅ 主播稿语音文件处理完成")
    
    def _on_voice_generation_error(self, error_msg):
        """语音文件生成错误处理"""
        # 恢复按钮状态
        self.main_controller.voice_button.configure(text="主播稿", state="normal")
        
        # 显示错误消息
        messagebox.showerror("语音文件生成失败", error_msg)
        print(f"❌ 语音文件生成失败: {error_msg}")
    
    def _on_tts_generation_complete(self, tts_result, total_count, original_existing_count):
        """TTS生成完成处理"""
        # 恢复按钮状态
        self.main_controller.voice_button.configure(text="主播稿", state="normal")
        
        try:
            if tts_result.get("success"):
                # TTS生成成功
                missing_count = tts_result.get("missing_count", 0)
                success_count = tts_result.get("success_count", 0)
                error_count = tts_result.get("error_count", 0)
                
                # 构建详细的结果消息
                result_message = f"主播稿语音文件处理完成！\n\n"
                result_message += f"📊 文件统计：\n"
                result_message += f"• 总计文本条目: {total_count} 条\n"
                result_message += f"• 原有语音文件: {original_existing_count} 条\n"
                result_message += f"• 发现缺失文件: {missing_count} 条\n\n"
                
                result_message += f"🎵 TTS生成结果：\n"
                result_message += f"• 成功生成: {success_count} 条\n"
                result_message += f"• 生成失败: {error_count} 条\n\n"
                
                final_existing_count = original_existing_count + success_count
                final_missing_count = total_count - final_existing_count
                
                result_message += f"🏁 最终状态：\n"
                result_message += f"• 可用语音文件: {final_existing_count} 条\n"
                result_message += f"• 仍缺失文件: {final_missing_count} 条\n\n"
                
                if final_missing_count == 0:
                    result_message += "✅ 所有语音文件都已准备就绪！"
                    messagebox.showinfo("主播稿语音文件", result_message)
                elif error_count > 0:
                    result_message += f"⚠️ 有 {error_count} 个文件生成失败，请检查网络连接或重试"
                    messagebox.showwarning("主播稿语音文件", result_message)
                else:
                    result_message += "✅ TTS生成完成！"
                    messagebox.showinfo("主播稿语音文件", result_message)
                
                print(f"✅ TTS生成完成：成功 {success_count} 条，失败 {error_count} 条")
                
            else:
                # TTS生成失败
                error_msg = tts_result.get("error", "未知错误")
                result_message = f"TTS语音生成失败！\n\n错误信息：{error_msg}\n\n请检查网络连接和TTS服务配置"
                messagebox.showerror("TTS生成失败", result_message)
                print(f"❌ TTS生成失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"处理TTS生成结果时出错: {str(e)}"
            messagebox.showerror("错误", error_msg)
            print(f"❌ 处理TTS生成结果失败: {e}") 

    def _on_server_start_error(self, error_msg):
        """服务器启动失败处理"""
        messagebox.showerror("服务器启动失败", error_msg)
        self.start_server_button.configure(state="normal")
        self.server_status_label.configure(text="状态: 启动失败", foreground="red")
    
    def _on_server_stopped(self):
        """服务器停止成功处理"""
        self.server_status_label.configure(text="状态: 已停止", foreground="gray")
        self.start_server_button.configure(state="normal")
        self.stop_server_button.configure(state="disabled")
        self.send_button.configure(state="disabled")
        self.client_count_label.configure(text="连接数: 0")
        
        # 停止状态更新任务
        if self.message_update_job:
            self.root.after_cancel(self.message_update_job)
            self.message_update_job = None
    
    def _on_server_stop_error(self, error_msg):
        """服务器停止失败处理"""
        messagebox.showerror("服务器停止失败", error_msg)
        self.stop_server_button.configure(state="normal") 