import asyncio
import json
import os
import pygame
import glob
import threading
from datetime import datetime
from typing import Dict, Any, Optional, List
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ListenerPlugin:
    """监听插件 - 独立的时间同步和音频播放监听服务"""
    
    def __init__(self, wb_server=None):
        self.wb_server = wb_server
        self.is_listening = False  # 监听状态
        self.voice_data = {}  # 语音数据缓存
        self.zhibo_file_path = None  # 当前使用的直播数据文件
        
        # 时间匹配设置
        self.time_tolerance = 1.0  # 时间容差（秒）
        self.last_played_time = -1  # 上次播放的时间点
        
        # 音频播放相关
        self.audio_thread = None
        self.is_audio_playing = False
        self.current_audio_duration = 0
        self._pending_goon_command = False  # 待发送的继续命令标志
        
        # 新增：序列播放相关
        self.sequence_thread = None
        self.is_sequence_playing = False
        self.sequence_data = []  # 序列播放数据
        
        # 初始化pygame音频
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            logger.info("✅ pygame音频模块初始化成功")
        except Exception as e:
            logger.error(f"❌ pygame音频模块初始化失败: {e}")
  
    def load_latest_zhibo_data(self, cache_key=None) -> bool:
        """加载直播数据文件 - 支持根据cache_key查找对应的语音缓存文件"""
        try:
            zhibo_dir = "src/cache/zhibo"
            if not os.path.exists(zhibo_dir):
                logger.error(f"❌ 直播数据目录不存在: {zhibo_dir}")
                return False
            
            target_file = None
            
            if cache_key:
                # 查找语音缓存文件（voice_前缀）
                voice_filename = f"voice_{cache_key}.json"
                voice_file_path = os.path.join(zhibo_dir, voice_filename)
                
                if os.path.exists(voice_file_path):
                    target_file = voice_file_path
                    logger.info(f"🎯 找到cache_key对应的语音缓存文件: {voice_filename}")
                else:
                    logger.warning(f"⚠️ 未找到cache_key对应的语音文件 {voice_filename}")
                    # 尝试查找主播稿文件作为备用
                    zhubo_filename = f"{cache_key}.json"
                    zhubo_file_path = os.path.join(zhibo_dir, zhubo_filename)
                    if os.path.exists(zhubo_file_path):
                        logger.warning(f"⚠️ 找到主播稿文件但需要生成语音数据: {zhubo_filename}")
                        return False
            
            if not target_file:
                # 查找所有语音缓存文件
                voice_files = glob.glob(os.path.join(zhibo_dir, "voice_*.json"))
                if voice_files:
                    target_file = max(voice_files, key=os.path.getmtime)
                    logger.info(f"📁 使用最新的语音缓存文件: {os.path.basename(target_file)}")
                else:
                    logger.error(f"❌ 未找到语音缓存文件")
                    return False
            
            self.zhibo_file_path = target_file
            
            # 加载语音缓存数据
            with open(target_file, 'r', encoding='utf-8') as f:
                zhibo_data = json.load(f)

                # 检查是否是新的优化版缓存
                if zhibo_data.get("md5_encoding", False):
                    # 新版：使用MD5即时编码，不需要voice_data映射
                    logger.info("🔧 检测到优化版缓存，使用MD5即时编码模式")
                    self.voice_data = {}  # 保持为空，使用MD5即时编码
                    self.use_md5_encoding = True
                else:
                    # 旧版：使用voice_data映射
                    logger.info("📋 使用传统voice_data映射模式")
                    raw_voice_data = zhibo_data.get("voice_data", {})

                    # 转换数据格式：字符串路径 -> 包含path和is_play的字典
                    self.voice_data = {}
                    for time_str, path in raw_voice_data.items():
                        self.voice_data[time_str] = {
                            "path": path,
                            "is_play": False  # 默认未播放
                        }
                    self.use_md5_encoding = False

                # 记录文件来源信息
                source_cache_key = zhibo_data.get("cache_key", "未知")
                if source_cache_key != "未知" and cache_key and source_cache_key != cache_key:
                    logger.warning(f"⚠️ 文件中的cache_key ({source_cache_key}) 与请求的cache_key ({cache_key}) 不匹配")

            # 尝试加载原始文本数据（用于MD5即时编码）
            self._load_original_texts(cache_key or source_cache_key)

            logger.info(f"✅ 加载语音缓存数据成功: {os.path.basename(target_file)}")
            logger.info(f"   语音数量: {len(self.voice_data)}")
            logger.info(f"   缓存key: {zhibo_data.get('cache_key', '未知')}")
            if hasattr(self, 'original_texts'):
                logger.info(f"   原始文本数量: {len(self.original_texts)}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载直播数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def prepare_sequence_data(self) -> List[Dict[str, Any]]:
        """准备序列播放数据 - 筛选小于7秒的数据并按时间排序"""
        try:
            sequence_items = []
            
            # 优先使用MD5即时编码方法
            if (hasattr(self, 'use_md5_encoding') and self.use_md5_encoding and
                hasattr(self, 'original_texts') and self.original_texts):

                logger.debug("🔧 使用MD5即时编码准备序列数据")
                for time_str, text_content in self.original_texts.items():
                    try:
                        time_value = float(time_str)
                        if time_value < 7.0:  # 小于7秒的数据
                            audio_path = self._generate_audio_path_from_text(text_content)
                            sequence_items.append({
                                "time": time_value,
                                "time_str": time_str,
                                "path": audio_path,
                                "is_play": self._is_audio_played(time_str)
                            })
                    except ValueError:
                        logger.warning(f"⚠️ 无效的时间格式: {time_str}")
                        continue
            else:
                # 使用传统voice_data方法
                logger.debug("📋 使用传统voice_data准备序列数据")
                for time_str, voice_item in self.voice_data.items():
                    try:
                        time_value = float(time_str)
                        if time_value < 7.0:  # 小于7秒的数据
                            sequence_items.append({
                                "time": time_value,
                                "time_str": time_str,
                                "path": voice_item["path"],
                                "is_play": voice_item.get("is_play", False)
                            })
                    except ValueError:
                        logger.warning(f"⚠️ 无效的时间格式: {time_str}")
                        continue
            
            # 按时间排序
            sequence_items.sort(key=lambda x: x["time"])
            
            logger.info(f"📋 准备序列播放数据：{len(sequence_items)} 条 (< 7秒)")
            for item in sequence_items:
                logger.info(f"   {item['time']}s: {os.path.basename(item['path'])}")
            
            return sequence_items
            
        except Exception as e:
            logger.error(f"❌ 准备序列播放数据失败: {e}")
            return []

    async def play_sequence(self):
        """播放序列数据"""
        try:
            if not self.sequence_data:
                logger.warning("⚠️ 没有序列播放数据")
                return
            
            self.is_sequence_playing = True
            logger.info("🎵 开始序列播放...")
            
            # 处理0秒数据
            zero_second_items = [item for item in self.sequence_data if item["time"] == 0.0]
            other_items = [item for item in self.sequence_data if item["time"] > 0.0]
            
            # 播放0秒数据
            for item in zero_second_items:
                if not self.is_sequence_playing:
                    break
                    
                logger.info(f"🎯 播放0秒数据: {os.path.basename(item['path'])}")
                success = self.play_audio_sync(item["path"])
                
                if success:
                    # 更新播放状态
                    self.voice_data[item["time_str"]]["is_play"] = True
                    self.save_zhibo_data()
            
            # 0秒数据播放完成后发送start命令
            if zero_second_items and self.wb_server:
                await self._send_websocket_command("start")
            
            # # 播放其他小于7秒的数据
            for item in other_items:
                if not self.is_sequence_playing:
                    break
                # 间隔3秒
                await asyncio.sleep(3)
                # 播放前发送pause命令
                if self.wb_server:
                    await self._send_websocket_command("pause")
                
                # 播放语音
                logger.info(f"🎵 播放 {item['time']}s: {os.path.basename(item['path'])}")
                success = self.play_audio_sync(item["path"])
                
                if success:
                    # 更新播放状态
                    self.voice_data[item["time_str"]]["is_play"] = True
                    self.save_zhibo_data()
                
                # 播放完成后发送goon命令
                if self.wb_server:
                    await self._send_websocket_command("goon")
                
            self.is_sequence_playing = False
            logger.info("✅ 序列播放完成")
            
        except Exception as e:
            logger.error(f"❌ 序列播放失败: {e}")
            self.is_sequence_playing = False

    async def _send_websocket_command(self, command: str):
        """发送WebSocket命令的统一接口"""
        try:
            if not self.wb_server:
                logger.warning("⚠️ WebSocket服务器未连接")
                return False
            
            # 调用wb_server的对应方法
            if command == "start":
                await self.wb_server.send_start_command()
            elif command == "pause":
                await self.wb_server.send_pause_command()
            elif command == "goon":
                await self.wb_server.send_goon_command()
            else:
                logger.warning(f"⚠️ 不支持的命令: {command}")
                return False
            
            logger.info(f"📤 已发送WebSocket命令: {command}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 发送WebSocket命令失败: {e}")
            return False

    def start_sequence_playback(self):
        """启动序列播放线程"""
        try:
            # 准备序列数据
            self.sequence_data = self.prepare_sequence_data()
            
            if not self.sequence_data:
                logger.warning("⚠️ 没有小于7秒的语音数据")
                return False
            
            # 启动序列播放线程
            def sequence_task():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.play_sequence())
                loop.close()
            
            self.sequence_thread = threading.Thread(target=sequence_task)
            self.sequence_thread.daemon = True
            self.sequence_thread.start()
            
            logger.info("🚀 序列播放线程已启动")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动序列播放失败: {e}")
            return False

    def save_zhibo_data(self):
        """保存直播播放状态到专门的状态文件"""
        try:
            # 生成状态文件路径（在同目录下创建_status.json文件）
            base_name = os.path.splitext(os.path.basename(self.zhibo_file_path))[0]
            status_filename = f"{base_name}_status.json"
            status_file_path = os.path.join(os.path.dirname(self.zhibo_file_path), status_filename)
            
            # 准备状态数据（只保存播放状态信息）
            status_data = {
                "voice_play_status": {},
                "last_played_time": self.last_played_time,
                "timestamp": datetime.now().isoformat(),
                "source_file": os.path.basename(self.zhibo_file_path)
            }
            
            # 提取播放状态信息
            for time_str, voice_item in self.voice_data.items():
                status_data["voice_play_status"][time_str] = voice_item.get("is_play", False)
            
            # 保存状态文件
            with open(status_file_path, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 播放状态已保存: {status_filename}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存播放状态失败: {e}")
            return False
    
    def load_play_status(self):
        """加载播放状态文件"""
        try:
            if not self.zhibo_file_path:
                return False
            
            # 生成状态文件路径
            base_name = os.path.splitext(os.path.basename(self.zhibo_file_path))[0]
            status_filename = f"{base_name}_status.json"
            status_file_path = os.path.join(os.path.dirname(self.zhibo_file_path), status_filename)
            
            if not os.path.exists(status_file_path):
                logger.info(f"📄 播放状态文件不存在，使用默认状态: {status_filename}")
                return True
            
            # 加载状态数据
            with open(status_file_path, 'r', encoding='utf-8') as f:
                status_data = json.load(f)
            
            # 应用播放状态
            voice_play_status = status_data.get("voice_play_status", {})
            for time_str, is_played in voice_play_status.items():
                if time_str in self.voice_data:
                    self.voice_data[time_str]["is_play"] = is_played
            
            # 恢复最后播放时间
            self.last_played_time = status_data.get("last_played_time", -1)
            
            logger.info(f"✅ 播放状态已加载: {status_filename}")
            logger.info(f"   最后播放时间: {self.last_played_time}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载播放状态失败: {e}")
            return False

    def find_matching_audio(self, current_time: float) -> Optional[Dict[str, Any]]:
        """查找匹配当前时间的音频 - 优化版：优先使用MD5即时编码查找"""
        try:
            # 优先使用MD5即时编码方法
            if (hasattr(self, 'use_md5_encoding') and self.use_md5_encoding and
                hasattr(self, 'original_texts') and self.original_texts):

                logger.debug("🔧 使用MD5即时编码查找音频")
                return self._find_matching_audio_md5(current_time)

            # 如果有原始文本数据，也尝试MD5方法
            elif hasattr(self, 'original_texts') and self.original_texts:
                logger.debug("📝 使用原始文本MD5编码查找音频")
                return self._find_matching_audio_md5(current_time)

            # 回退到传统voice_data方法
            else:
                logger.debug("📋 使用传统voice_data查找音频")
                return self._find_matching_audio_legacy(current_time)

        except Exception as e:
            logger.error(f"❌ 查找匹配音频失败: {e}")
            return None

    def _find_matching_audio_md5(self, current_time: float) -> Optional[Dict[str, Any]]:
        """使用MD5即时编码查找匹配音频"""
        try:
            for time_str in self.original_texts.keys():
                target_time = float(time_str)

                # 检查时间是否在容差范围内，且该音频尚未播放
                if (abs(current_time - target_time) <= self.time_tolerance and
                    not self._is_audio_played(time_str) and
                    target_time > self.last_played_time):

                    # 通过MD5即时编码生成文件路径
                    text_content = self.original_texts[time_str]
                    audio_path = self._generate_audio_path_from_text(text_content)

                    # 检查文件是否存在
                    if not os.path.exists(audio_path):
                        logger.warning(f"⚠️ 音频文件不存在: {audio_path}")
                        continue

                    return {
                        "time": target_time,
                        "time_str": time_str,
                        "path": audio_path,
                        "is_play": self._is_audio_played(time_str)
                    }

            return None

        except Exception as e:
            logger.error(f"❌ MD5编码查找音频失败: {e}")
            return None

    def _find_matching_audio_legacy(self, current_time: float) -> Optional[Dict[str, Any]]:
        """旧版查找匹配音频方法 - 使用voice_data映射"""
        try:
            for time_str, voice_item in self.voice_data.items():
                target_time = float(time_str)

                # 检查时间是否在容差范围内，且该音频尚未播放
                if (abs(current_time - target_time) <= self.time_tolerance and
                    not voice_item.get("is_play", False) and
                    target_time > self.last_played_time):

                    return {
                        "time": target_time,
                        "time_str": time_str,
                        "path": voice_item["path"],
                        "is_play": voice_item["is_play"]
                    }

            return None

        except Exception as e:
            logger.error(f"❌ 旧版查找匹配音频失败: {e}")
            return None

    def _generate_audio_path_from_text(self, text: str) -> str:
        """通过文本内容生成音频文件路径"""
        try:
            # 导入TTS哈希基类来生成路径
            from src.utils.tts_hash_base import TTSHashBase

            # 创建临时实例来生成路径
            hash_generator = TTSHashBase()
            return hash_generator._generate_text_path(text)

        except Exception as e:
            logger.error(f"❌ 生成音频路径失败: {e}")
            # 回退到空路径
            return ""

    def _is_audio_played(self, time_str: str) -> bool:
        """检查音频是否已播放"""
        # 如果有voice_data，使用其中的播放状态
        if hasattr(self, 'voice_data') and time_str in self.voice_data:
            return self.voice_data[time_str].get("is_play", False)

        # 否则检查播放状态缓存
        if hasattr(self, 'played_audio_cache'):
            return time_str in self.played_audio_cache

        return False

    def _mark_audio_played(self, time_str: str):
        """标记音频为已播放"""
        # 如果有voice_data，更新其播放状态
        if hasattr(self, 'voice_data') and time_str in self.voice_data:
            self.voice_data[time_str]["is_play"] = True

        # 同时更新播放状态缓存
        if not hasattr(self, 'played_audio_cache'):
            self.played_audio_cache = set()
        self.played_audio_cache.add(time_str)

    def _load_original_texts(self, cache_key: str):
        """加载原始文本数据用于MD5即时编码 - 修复版"""
        try:
            if not cache_key or cache_key == "未知":
                logger.warning("⚠️ 无有效cache_key，跳过原始文本加载")
                return

            # 查找主播稿文件
            zhibo_dir = "src/cache/zhibo"
            zhubo_filename = f"{cache_key}.json"
            zhubo_file_path = os.path.join(zhibo_dir, zhubo_filename)

            if not os.path.exists(zhubo_file_path):
                logger.warning(f"⚠️ 主播稿文件不存在: {zhubo_filename}")
                return

            # 加载主播稿数据
            with open(zhubo_file_path, 'r', encoding='utf-8') as f:
                zhubo_data = json.load(f)

            # 提取文本内容
            self.original_texts = {}

            logger.info(f"🔍 开始从主播稿文件提取原始文本: {len(zhubo_data)} 个项目")

            for item in zhubo_data:
                if not isinstance(item, dict) or 'list' not in item:
                    continue

                item_list = item['list']
                if not isinstance(item_list, dict):
                    continue

                for time_key, content in item_list.items():
                    # 处理不同的内容格式
                    text_content = None
                    if isinstance(content, str) and content.strip():
                        # 直接是字符串（旧格式兼容）
                        text_content = content.strip()
                    elif isinstance(content, dict) and 'txt' in content:
                        # 字典格式，包含txt字段（新格式）
                        txt_value = content['txt']
                        if isinstance(txt_value, str) and txt_value.strip():
                            text_content = txt_value.strip()

                    # 只保存有效的数字时间戳
                    if text_content and self._is_numeric_timestamp(time_key):
                        self.original_texts[time_key] = text_content

            logger.info(f"✅ 加载原始文本数据成功: {len(self.original_texts)} 条")

            # 打印前几条提取的文本用于调试
            if self.original_texts:
                logger.info("📋 提取的文本示例:")
                for i, (timestamp, text) in enumerate(list(self.original_texts.items())[:3]):
                    logger.info(f"   {i+1}. {timestamp}: {text[:50]}...")

        except Exception as e:
            logger.error(f"❌ 加载原始文本数据失败: {e}")
            import traceback
            traceback.print_exc()
            # 不影响主流程，继续使用voice_data方式

    def _is_numeric_timestamp(self, timestamp: str) -> bool:
        """检查时间戳是否为数字格式"""
        try:
            float(timestamp)
            return True
        except ValueError:
            return False

    def play_audio_sync(self, audio_path: str) -> bool:
        """同步播放音频文件"""
        try:
            # 处理相对路径
            if audio_path.startswith("./"):
                audio_path = audio_path[2:]
            
            # 确保文件存在
            if not os.path.exists(audio_path):
                logger.error(f"❌ 音频文件不存在: {audio_path}")
                return False
            
            logger.info(f"🎵 开始播放音频: {os.path.basename(audio_path)}")
            
            # 加载并播放音频
            pygame.mixer.music.load(audio_path)
            pygame.mixer.music.play()
            
            # 等待播放完成
            while pygame.mixer.music.get_busy():
                pygame.time.wait(100)  # 等待100毫秒
            
            logger.info(f"✅ 音频播放完成: {os.path.basename(audio_path)}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 播放音频失败: {e}")
            return False

    def _send_goon_command_threadsafe(self):
        """线程安全地发送继续播放命令"""
        try:
            if hasattr(self.wb_server, 'server_task') and self.wb_server.server_task:
                # 获取服务器的事件循环
                loop = self.wb_server.server_task.get_loop()
                if loop and not loop.is_closed():
                    # 在服务器的事件循环中调度协程
                    asyncio.run_coroutine_threadsafe(
                        self.wb_server.send_goon_command(), 
                        loop
                    )
                    logger.info("📤 已调度继续播放命令")
                    return
            
            # 备用方案：设置一个标志，让主循环发送命令
            self._pending_goon_command = True
            logger.info("📤 已标记继续播放命令（待发送）")
                
        except Exception as e:
            logger.warning(f"⚠️ 发送继续命令时出错: {e}")
            # 设置标志作为备用
            self._pending_goon_command = True

    async def play_audio_async(self, audio_path: str, time_str: str):
        """异步播放音频的包装函数"""
        def audio_task():
            try:
                self.is_audio_playing = True
                success = self.play_audio_sync(audio_path)
                
                if success:
                    # 更新播放状态 - 使用新的标记方式
                    self._mark_audio_played(time_str)
                    # 保存到文件
                    self.save_zhibo_data()
                    logger.info(f"✅ 音频播放状态已更新: {time_str}")
                
                self.is_audio_playing = False
                
                # 播放完成后发送继续命令 - 使用线程安全的方式
                if self.wb_server:
                    self._send_goon_command_threadsafe()
                
            except Exception as e:
                logger.error(f"❌ 异步音频播放失败: {e}")
                self.is_audio_playing = False
        
        # 在线程中执行音频播放
        self.audio_thread = threading.Thread(target=audio_task)
        self.audio_thread.daemon = True
        self.audio_thread.start()

    async def handle_time_update(self, current_time: float):
        """处理时间更新消息"""
        try:
            if not self.is_listening:
                return
            
            # 检查是否有待发送的继续命令
            if self._pending_goon_command:
                self._pending_goon_command = False
                if self.wb_server:
                    await self.wb_server.send_goon_command()
                    logger.info("📤 已发送待定的继续播放命令")
            
            # 如果正在序列播放，跳过时间匹配逻辑
            if self.is_sequence_playing:
                return
            
            # 检查是否正在播放音频
            if self.is_audio_playing:
                return
            
            # 查找匹配的音频
            match_result = self.find_matching_audio(current_time)
            if not match_result:
                return
            
            target_time = match_result["time"]
            time_str = match_result["time_str"]
            audio_path = match_result["path"]
            
            logger.info(f"🎯 时间匹配成功: 当前={current_time:.2f}s, 目标={target_time:.2f}s")
            
            # 更新最后播放时间
            self.last_played_time = target_time
            # 延迟3秒
            await asyncio.sleep(3)
            # 发送暂停命令
            if self.wb_server:
                await self.wb_server.send_pause_command()
                logger.info("⏸️ 已发送暂停命令")
            
            # 异步播放音频
            await self.play_audio_async(audio_path, time_str)
            
        except Exception as e:
            logger.error(f"❌ 处理时间更新失败: {e}")

    def start_listening(self, cache_key: str = None) -> bool:
        """开始监听"""
        try:
            if self.is_listening:
                logger.warning("⚠️ 监听已在进行中")
                return True
            
            # 加载直播数据 - 传递cache_key参数
            if not self.load_latest_zhibo_data(cache_key):
                return False
            
            # 加载播放状态
            self.load_play_status()
            
            # 重置状态
            self.last_played_time = -1
            self.is_audio_playing = False
            self.is_sequence_playing = False
            
            # 重置所有音频的播放状态为False
            if hasattr(self, 'voice_data') and self.voice_data:
                for voice_item in self.voice_data.values():
                    voice_item["is_play"] = False

            # 重置播放状态缓存
            if hasattr(self, 'played_audio_cache'):
                self.played_audio_cache = set()

            # 保存重置状态
            self.save_zhibo_data()
            
            self.is_listening = True
            logger.info("🎧 监听服务已开始")
            if cache_key:
                logger.info(f"   缓存key: {cache_key}")
            
            # 启动序列播放
            self.start_sequence_playback()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 开始监听失败: {e}")
            return False

    def stop_listening(self) -> bool:
        """停止监听"""
        try:
            if not self.is_listening:
                logger.warning("⚠️ 监听未在进行中")
                return True
            
            self.is_listening = False
            
            # 停止序列播放
            self.is_sequence_playing = False
            
            # 停止当前音频播放
            if pygame.mixer.music.get_busy():
                pygame.mixer.music.stop()
            
            # 等待音频线程结束
            if self.audio_thread and self.audio_thread.is_alive():
                self.audio_thread.join(timeout=2)
            
            # 等待序列播放线程结束
            if self.sequence_thread and self.sequence_thread.is_alive():
                self.sequence_thread.join(timeout=2)
            
            self.is_audio_playing = False
            logger.info("🛑 监听服务已停止")
            return True
            
        except Exception as e:
            logger.error(f"❌ 停止监听失败: {e}")
            return False

    def get_listener_status(self) -> Dict[str, Any]:
        """获取监听状态"""
        # 计算语音数量和播放数量
        if (hasattr(self, 'use_md5_encoding') and self.use_md5_encoding and
            hasattr(self, 'original_texts') and self.original_texts):
            voice_count = len(self.original_texts)
            played_count = sum(1 for time_str in self.original_texts.keys()
                             if self._is_audio_played(time_str))
        else:
            voice_count = len(self.voice_data)
            played_count = sum(1 for item in self.voice_data.values() if item.get("is_play", False))

        return {
            "is_listening": self.is_listening,
            "is_audio_playing": self.is_audio_playing,
            "is_sequence_playing": self.is_sequence_playing,
            "voice_count": voice_count,
            "played_count": played_count,
            "last_played_time": self.last_played_time,
            "zhibo_file": os.path.basename(self.zhibo_file_path) if self.zhibo_file_path else None,
            "sequence_count": len(self.sequence_data) if hasattr(self, 'sequence_data') else 0,
            "md5_encoding": getattr(self, 'use_md5_encoding', False)
        }

    def reset_play_status(self) -> bool:
        """重置所有音频的播放状态"""
        try:
            # 重置播放状态缓存
            if hasattr(self, 'played_audio_cache'):
                self.played_audio_cache.clear()

            # 如果使用传统voice_data，也重置其状态
            if hasattr(self, 'voice_data') and self.voice_data:
                for voice_item in self.voice_data.values():
                    voice_item["is_play"] = False

            self.last_played_time = -1
            self.save_zhibo_data()

            logger.info("🔄 所有音频播放状态已重置")
            return True

        except Exception as e:
            logger.error(f"❌ 重置播放状态失败: {e}")
            return False

    async def play_voice_by_key(self, time_key: str) -> Dict[str, Any]:
        """根据指定的时间key播放对应的语音 - 优化版：支持MD5即时编码"""
        try:
            audio_path = None

            # 优先使用MD5即时编码方法
            if (hasattr(self, 'use_md5_encoding') and self.use_md5_encoding and
                hasattr(self, 'original_texts') and self.original_texts):

                if time_key not in self.original_texts:
                    error_msg = f"时间key '{time_key}' 不存在于原始文本数据中"
                    logger.error(f"❌ {error_msg}")
                    return {"success": False, "error": error_msg}

                # 通过MD5即时编码生成文件路径
                text_content = self.original_texts[time_key]
                audio_path = self._generate_audio_path_from_text(text_content)

            else:
                # 使用传统voice_data方法
                if time_key not in self.voice_data:
                    error_msg = f"时间key '{time_key}' 不存在于语音数据中"
                    logger.error(f"❌ {error_msg}")
                    return {"success": False, "error": error_msg}

                voice_item = self.voice_data[time_key]
                audio_path = voice_item["path"]

            # 检查音频文件是否存在
            if not os.path.exists(audio_path):
                error_msg = f"音频文件不存在: {audio_path}"
                logger.error(f"❌ {error_msg}")
                return {"success": False, "error": error_msg}

            # 发送暂停命令（如果有WebSocket服务器连接）
            # if self.wb_server:
            #     await self._send_websocket_command("pause")
            #     logger.info("⏸️ 已发送暂停命令")

            # 异步播放音频
            await self.play_audio_async(audio_path, time_key)

            return {
                "success": True,
                "message": f"开始播放时间点 {time_key} 的语音",
                "audio_file": os.path.basename(audio_path),
                "time_key": time_key
            }
            
        except Exception as e:
            error_msg = f"播放语音失败: {e}"
            logger.error(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}

    def get_voice_info_by_key(self, time_key: str) -> Dict[str, Any]:
        """获取指定时间key的语音信息"""
        try:
            if time_key not in self.voice_data:
                return {"success": False, "error": f"时间key '{time_key}' 不存在"}
            
            voice_item = self.voice_data[time_key]
            
            return {
                "success": True,
                "audio_path": voice_item["path"],
                "audio_file": os.path.basename(voice_item["path"]),
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
